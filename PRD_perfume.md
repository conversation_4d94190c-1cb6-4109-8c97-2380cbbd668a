# Fragrance Companion App – Product Requirements Document (PRD)

## 📘 Overview
**Fragrance Companion** is a mobile/web app that helps users discover, layer, and optimize their fragrance use based on their existing collection, seasonal preferences, mood, and wardrobe color. The app provides personalized suggestions and encourages community sharing through reviews and stack (layering) recommendations.

---

## 🎯 Goals
- Help users **maximize fragrance value** by suggesting personalized scent combos based on what they already own.
- Provide **purchase recommendations** to enhance or expand a user's scent library.
- Foster a **social community** around fragrance use, reviews, and scent stacking.
- Encourage **experimentation** with layering to improve longevity and uniqueness of scent.

---

## 📱 Core Features

### 1. Quick Pick Engine
- Users can start with one of the following:
  - **Season** (e.g., Summer, Winter)
  - **Mood** (e.g., Seductive, Calm)
  - **Color** (based on outfit, e.g., Yellow = citrusy)
  - **Shuffle Button** for random recommendations
- Engine uses tag-matching logic to return suitable fragrances or combos.

### 2. My Fragrances (User Inventory)
- Users can add fragrances manually or via search.
- Fields include:
  - Name, Brand
  - Date Purchased
  - Size
  - Longevity Rating
  - Favorite Use (Season, Mood, Color)
  - Perceived Notes
- Primary dataset used for suggestion logic.
- App prioritizes layering options using only what user owns.

### 3. Smart Layering Suggestions
- After user selects a fragrance (manually or via Quick Pick):
  - Suggests 1–2 additional fragrances from **My Fragrances** that enhance longevity or scent profile.
  - If no match is found, falls back to **Buy Recommendations**.

### 4. Buy Recommendations
- Fragrances suggested based on:
  - User’s gaps in notes or categories
  - Season/Mood/Color intent
  - Community trends
- Filters:
  - Price
  - Longevity
  - Popularity
  - Dupes Available

---

## 🧑‍🤝‍🧑 Social Features

### 5. User Profiles
- Public or private
- Includes:
  - Collection (“My Fragrances”)
  - Wishlist
  - Reviews
  - Layering stacks

### 6. Fragrance Reviews
- Users can:
  - Leave reviews per fragrance
  - Rate longevity, projection, wear time
  - Tag season, mood, and layering tips

### 7. Layering “Stacks”
- Users can create and share their favorite scent combos
- Stack entry includes:
  - 2–3 fragrances
  - Stack title & description
  - Tags: Mood, Season, Occasion
  - Optional before/after wear tips

### 8. Community Feed
- Browse top-rated stacks
- Comment, like, bookmark
- Leaderboards:
  - Most Helpful Reviewer
  - Top Stack Creators
  - Trending Layer Combos

---

## 🔍 Technical Specifications

### Backend
- **Database**: PostgreSQL / Supabase
  - Tables: Users, Fragrances, Reviews, Stacks, Tags
- **Auth**: Firebase / Auth0
- **Optional APIs**:
  - FragranceNet or Fragrantica scraping to autofill fragrance data

### Frontend
- **Mobile**: React Native
- **Web**: Next.js + TailwindCSS
- **Search**: Full-text search on fragrance name, notes, tags
- **State Management**: Redux or Zustand

---

## 🧠 Logic Engine

### Tagging System
Each fragrance is tagged with:
- **Season** (e.g., Summer = Citrus, Aquatic)
- **Mood** (e.g., Calm = Lavender, Powdery)
- **Color** (e.g., Red = Spicy, Warm)
- **Gender** (optional: Male, Female, Unisex)
- **Note Types** (Top, Middle, Base)

### Matching Logic
- Quick Pick or Shuffle pulls matching entries from tag library
- Layering logic checks:
  - Compatibility of base/mid/top notes
  - Longevity enhancement patterns
  - Sillage boosting ingredients (e.g., amber, vanilla)

---

## 📌 App Navigation

- **Home**: Quick Pick, Trending Stack, Randomizer
- **My Fragrances**: Collection, Add New, Suggest Layers
- **Discover**: New picks, Purchase Suggestions
- **Community**: Reviews, Stacks, Leaderboard
- **Profile**: My Stacks, Reviews, Wishlist, Settings

---

## 📆 Milestones

| Milestone | Description | Target Date |
|----------|-------------|-------------|
| MVP Design | Wireframes, Database Schema | Aug 2025 |
| Alpha Build | Core functions (Add fragrance, Suggest combos, Stack creation) | Sept 2025 |
| Beta Launch | Social feed, Reviews, User auth | Oct 2025 |
| Public Release | Full functionality, App Store & Web launch | Dec 2025 |

---

## 📈 Future Enhancements
- Barcode scanning to auto-add fragrances
- Skin-type-aware longevity predictors
- AI scent matcher using ingredient database
- Notifications for seasonal combos or trending stacks
