#!/usr/bin/env python3
"""
CSV Consolidation Script
Consolidates all CSV files in the CSV directory into a single merged file.
"""

import pandas as pd
import os
import glob
from datetime import datetime
import chardet

def detect_delimiter(file_path, sample_size=1024):
    """Detect the delimiter used in a CSV file."""
    with open(file_path, 'rb') as f:
        sample = f.read(sample_size).decode('utf-8', errors='ignore')
    
    # Count occurrences of common delimiters
    delimiters = [',', ';', '\t', '|']
    delimiter_counts = {}
    
    for delimiter in delimiters:
        delimiter_counts[delimiter] = sample.count(delimiter)
    
    # Return the delimiter with the highest count
    return max(delimiter_counts, key=delimiter_counts.get)

def detect_encoding(file_path):
    """Detect the encoding of a file."""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_csv_safe(file_path):
    """Safely read a CSV file with automatic delimiter and encoding detection."""
    try:
        # Detect encoding
        encoding = detect_encoding(file_path)
        print(f"Detected encoding for {file_path}: {encoding}")
        
        # Detect delimiter
        delimiter = detect_delimiter(file_path)
        print(f"Detected delimiter for {file_path}: '{delimiter}'")
        
        # Read the CSV
        df = pd.read_csv(file_path, delimiter=delimiter, encoding=encoding, low_memory=False)
        return df, delimiter, encoding
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        # Fallback to default settings
        try:
            df = pd.read_csv(file_path, encoding='utf-8', low_memory=False)
            return df, ',', 'utf-8'
        except:
            df = pd.read_csv(file_path, encoding='latin-1', low_memory=False)
            return df, ',', 'latin-1'

def create_unified_schema():
    """Define the unified schema for all CSV files."""
    return {
        'source_file': '',
        'name': '',
        'brand': '',
        'description': '',
        'type_concentration': '',
        'release_year': '',
        'country': '',
        'gender': '',
        'price': '',
        'price_currency': '',
        'availability': '',
        'sold_count': '',
        'rating_value': '',
        'rating_count': '',
        'top_notes': '',
        'middle_notes': '',
        'base_notes': '',
        'main_accords': '',
        'perfumers': '',
        'url': '',
        'image_url': '',
        'item_location': '',
        'last_updated': ''
    }

def map_ebay_columns(df):
    """Map eBay CSV columns to unified schema."""
    mapping = {
        'title': 'name',
        'brand': 'brand',
        'type': 'type_concentration',
        'price': 'price',
        'priceWithCurrency': 'price_currency',
        'available': 'availability',
        'availableText': 'availability',
        'sold': 'sold_count',
        'lastUpdated': 'last_updated',
        'itemLocation': 'item_location'
    }
    
    mapped_df = pd.DataFrame()
    schema = create_unified_schema()
    
    for unified_col in schema.keys():
        if unified_col == 'source_file':
            mapped_df[unified_col] = 'ebay_mens_perfume.csv'
        else:
            # Find the original column that maps to this unified column
            original_col = None
            for orig, unified in mapping.items():
                if unified == unified_col and orig in df.columns:
                    original_col = orig
                    break
            
            if original_col:
                mapped_df[unified_col] = df[original_col].astype(str)
            else:
                mapped_df[unified_col] = ''
    
    return mapped_df

def map_final_perfume_columns(df):
    """Map final_perfume_data CSV columns to unified schema."""
    mapping = {
        'Name': 'name',
        'Brand': 'brand',
        'Description': 'description',
        'Notes': 'top_notes',  # General notes go to top_notes
        'Image URL': 'image_url'
    }
    
    mapped_df = pd.DataFrame()
    schema = create_unified_schema()
    
    for unified_col in schema.keys():
        if unified_col == 'source_file':
            mapped_df[unified_col] = 'final_perfume_data.csv'
        else:
            original_col = None
            for orig, unified in mapping.items():
                if unified == unified_col and orig in df.columns:
                    original_col = orig
                    break
            
            if original_col:
                mapped_df[unified_col] = df[original_col].astype(str)
            else:
                mapped_df[unified_col] = ''
    
    return mapped_df

def map_fragrantica_columns(df):
    """Map Fragrantica CSV columns to unified schema."""
    mapping = {
        'Perfume': 'name',
        'Brand': 'brand',
        'Country': 'country',
        'Gender': 'gender',
        'Rating Value': 'rating_value',
        'Rating Count': 'rating_count',
        'Year': 'release_year',
        'Top': 'top_notes',
        'Middle': 'middle_notes',
        'Base': 'base_notes',
        'url': 'url'
    }
    
    mapped_df = pd.DataFrame()
    schema = create_unified_schema()
    
    for unified_col in schema.keys():
        if unified_col == 'source_file':
            mapped_df[unified_col] = 'fra_cleaned.csv'
        elif unified_col == 'perfumers':
            # Combine Perfumer1 and Perfumer2
            perfumer1 = df['Perfumer1'].astype(str) if 'Perfumer1' in df.columns else ''
            perfumer2 = df['Perfumer2'].astype(str) if 'Perfumer2' in df.columns else ''
            mapped_df[unified_col] = (perfumer1 + ', ' + perfumer2).str.replace(', nan', '').str.replace('nan, ', '').str.replace('nan', '')
        elif unified_col == 'main_accords':
            # Combine mainaccord1-5
            accords = []
            for i in range(1, 6):
                col_name = f'mainaccord{i}'
                if col_name in df.columns:
                    accords.append(df[col_name].astype(str))
            if accords:
                mapped_df[unified_col] = pd.concat(accords, axis=1).apply(
                    lambda x: ', '.join([str(val) for val in x if str(val) != 'nan' and str(val) != '']), axis=1
                )
            else:
                mapped_df[unified_col] = ''
        else:
            original_col = None
            for orig, unified in mapping.items():
                if unified == unified_col and orig in df.columns:
                    original_col = orig
                    break
            
            if original_col:
                mapped_df[unified_col] = df[original_col].astype(str)
            else:
                mapped_df[unified_col] = ''
    
    return mapped_df

def map_parfumo_columns(df):
    """Map Parfumo CSV columns to unified schema."""
    mapping = {
        'Name': 'name',
        'Brand': 'brand',
        'Release Year': 'release_year',
        'Concentration': 'type_concentration',
        'Rating Value': 'rating_value',
        'Rating Count': 'rating_count',
        'Main Accords': 'main_accords',
        'Top Notes': 'top_notes',
        'Middle Notes': 'middle_notes',
        'Base Notes': 'base_notes',
        'Perfumers': 'perfumers',
        'URL': 'url'
    }
    
    mapped_df = pd.DataFrame()
    schema = create_unified_schema()
    
    for unified_col in schema.keys():
        if unified_col == 'source_file':
            mapped_df[unified_col] = 'parfumo_datos.csv'
        else:
            original_col = None
            for orig, unified in mapping.items():
                if unified == unified_col and orig in df.columns:
                    original_col = orig
                    break
            
            if original_col:
                mapped_df[unified_col] = df[original_col].astype(str)
            else:
                mapped_df[unified_col] = ''
    
    return mapped_df

def main():
    """Main function to consolidate CSV files."""
    print("Starting CSV consolidation process...")
    print("=" * 50)
    
    # Find all CSV files in the CSV directory
    csv_files = glob.glob("CSV/*.csv")
    print(f"Found {len(csv_files)} CSV files:")
    for file in csv_files:
        print(f"  - {file}")
    print()
    
    consolidated_data = []
    file_stats = {}
    
    for csv_file in csv_files:
        print(f"Processing {csv_file}...")
        
        try:
            # Read the CSV file
            df, delimiter, encoding = read_csv_safe(csv_file)
            original_rows = len(df)
            print(f"  - Loaded {original_rows} rows with {len(df.columns)} columns")
            print(f"  - Columns: {list(df.columns)}")
            
            # Map columns based on file type
            filename = os.path.basename(csv_file)
            if 'ebay' in filename.lower():
                mapped_df = map_ebay_columns(df)
            elif 'final_perfume' in filename.lower():
                mapped_df = map_final_perfume_columns(df)
            elif 'fra_cleaned' in filename.lower():
                mapped_df = map_fragrantica_columns(df)
            elif 'parfumo' in filename.lower():
                mapped_df = map_parfumo_columns(df)
            else:
                print(f"  - Warning: Unknown file type for {filename}, skipping...")
                continue
            
            # Clean up the data
            mapped_df = mapped_df.replace('nan', '')
            mapped_df = mapped_df.replace('N/A', '')
            mapped_df = mapped_df.fillna('')

            # Ensure source_file is properly set
            mapped_df['source_file'] = filename
            
            consolidated_data.append(mapped_df)
            file_stats[filename] = {
                'original_rows': original_rows,
                'processed_rows': len(mapped_df),
                'delimiter': delimiter,
                'encoding': encoding
            }
            
            print(f"  - Successfully processed {len(mapped_df)} rows")
            print()
            
        except Exception as e:
            print(f"  - Error processing {csv_file}: {e}")
            print()
    
    if not consolidated_data:
        print("No data to consolidate!")
        return
    
    # Combine all dataframes
    print("Combining all data...")
    final_df = pd.concat(consolidated_data, ignore_index=True)
    
    # Add consolidation metadata
    final_df['consolidation_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Save the consolidated file
    output_file = "consolidated_perfume_data.csv"
    final_df.to_csv(output_file, index=False, encoding='utf-8')
    
    # Generate summary report
    print("=" * 50)
    print("CONSOLIDATION SUMMARY")
    print("=" * 50)
    print(f"Output file: {output_file}")
    print(f"Total rows in consolidated file: {len(final_df):,}")
    print(f"Total columns: {len(final_df.columns)}")
    print()
    
    print("File-by-file breakdown:")
    total_original_rows = 0
    for filename, stats in file_stats.items():
        print(f"  {filename}:")
        print(f"    - Original rows: {stats['original_rows']:,}")
        print(f"    - Processed rows: {stats['processed_rows']:,}")
        print(f"    - Delimiter: '{stats['delimiter']}'")
        print(f"    - Encoding: {stats['encoding']}")
        total_original_rows += stats['original_rows']
    
    print()
    print(f"Total original rows across all files: {total_original_rows:,}")
    print(f"Total consolidated rows: {len(final_df):,}")
    
    # Show column information
    print()
    print("Unified schema columns:")
    for i, col in enumerate(final_df.columns, 1):
        non_empty_count = (final_df[col] != '').sum()
        print(f"  {i:2d}. {col}: {non_empty_count:,} non-empty values ({non_empty_count/len(final_df)*100:.1f}%)")
    
    print()
    print("Consolidation completed successfully!")

if __name__ == "__main__":
    main()
