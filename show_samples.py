#!/usr/bin/env python3
import pandas as pd

def show_samples():
    df = pd.read_csv('consolidated_perfume_data.csv', low_memory=False)
    
    print('=== SAMPLE DATA FROM EACH SOURCE ===')
    print()
    
    # Sample from each source
    for source in df['source_file'].unique():
        print(f'--- {source} ---')
        sample = df[df['source_file'] == source].head(2)
        for idx, row in sample.iterrows():
            print(f'Name: {row["name"]}')
            print(f'Brand: {row["brand"]}')
            if row['description'] and str(row['description']) != 'nan' and str(row['description']) != '':
                desc = str(row['description'])[:100] + '...' if len(str(row['description'])) > 100 else str(row['description'])
                print(f'Description: {desc}')
            if row['top_notes'] and str(row['top_notes']) != 'nan' and str(row['top_notes']) != '':
                print(f'Top Notes: {row["top_notes"]}')
            if row['price'] and str(row['price']) != 'nan' and str(row['price']) != '':
                print(f'Price: {row["price"]}')
            if row['rating_value'] and str(row['rating_value']) != 'nan' and str(row['rating_value']) != '':
                print(f'Rating: {row["rating_value"]}')
            print()
        print()

if __name__ == "__main__":
    show_samples()
